package dev.pigmomo.yhkit2025.ui.dialog

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceCoupon
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceRedPacket
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 自动下单对话框
 *
 * @param autoOrderPlaceData 自动下单数据
 * @param onDismiss 对话框关闭回调
 * @param onConfirm 确认按钮回调，参数依次为：是否启用账号配置，是否启用地址配置，是否启用商品配置，是否启用订单配置，多线程配置数组
 * @param showMultiThreadDialog 是否显示多线程配置对话框的回调
 * @param multiThreadRangeList 多线程配置数组
 */
@Composable
fun OrderAutoPlaceDialog(
    autoOrderPlaceData: AutoOrderPlaceData,
    onDismiss: () -> Unit,
    onConfirm: (AutoOrderPlaceData, Boolean, Boolean, Boolean, Boolean, List<Int>) -> Unit,
    showMultiThreadDialog: () -> Unit,
    multiThreadRangeList: List<Int>,
    multiThreadEnabled: Boolean,
    setMultiThreadEnabled: (Boolean) -> Unit
) {
    val context = LocalContext.current

    var isAddAddress by remember { mutableStateOf(false) }
    var isAccountManager by remember { mutableStateOf(false) }
    var isAddCart by remember { mutableStateOf(false) }
    var isPlaceOrder by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = {
            Text("请选择下单流程")
        },
        text = {
            Column {
                // 多线程配置卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            text = "多线程",
                            onClick = {
                                if (!multiThreadEnabled) {
                                    showMultiThreadDialog()
                                } else {
                                    setMultiThreadEnabled(false)
                                }
                            },
                            tint = if (!multiThreadEnabled) Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                0xFF48454E
                            )
                        )

                        // 操作间隔

                    }
                }

                // 地址配置卡片
                AddressConfigCard(
                    isSelected = isAddAddress,
                    autoOrderPlaceData = autoOrderPlaceData,
                    onCardClick = { valid ->
                        if (valid) {
                            isAddAddress = !isAddAddress
                        } else {
                            Toast
                                .makeText(
                                    context,
                                    "请在 地址列表或添加地址页面 保存配置",
                                    Toast.LENGTH_SHORT
                                )
                                .show()
                            isAddAddress = false
                        }
                    }
                )

                // 账号配置卡片
                AccountConfigCard(
                    isSelected = isAccountManager,
                    autoOrderPlaceData = autoOrderPlaceData,
                    onCardClick = { valid ->
                        if (valid) {
                            isAccountManager = !isAccountManager
                        } else {
                            Toast
                                .makeText(
                                    context,
                                    "请在 账号信息页面 保存配置",
                                    Toast.LENGTH_SHORT
                                )
                                .show()
                            isAccountManager = false
                        }
                    }
                )

                // 商品配置卡片
                GoodsConfigCard(
                    isSelected = isAddCart,
                    autoOrderPlaceData = autoOrderPlaceData,
                    onCardClick = { valid ->
                        if (valid) {
                            isAddCart = !isAddCart
                        } else {
                            Toast
                                .makeText(
                                    context,
                                    "请在 添加购物车或购物车页面 保存配置",
                                    Toast.LENGTH_SHORT
                                )
                                .show()
                            isAddCart = false
                        }
                    }
                )

                // 订单配置卡片
                OrderConfigCard(
                    isSelected = isPlaceOrder,
                    autoOrderPlaceData = autoOrderPlaceData,
                    onCardClick = { valid ->
                        if (valid) {
                            isPlaceOrder = !isPlaceOrder
                        } else {
                            Toast
                                .makeText(
                                    context,
                                    "请在 订单提交页面 保存配置",
                                    Toast.LENGTH_SHORT
                                )
                                .show()
                            isPlaceOrder = false
                        }
                    }
                )
            }
        },
        confirmButton = {
            Button(
                enabled = isAddAddress || isAddCart || isPlaceOrder || isAccountManager,
                onClick = {
                    if (!isAddAddress && !isAddCart && !isPlaceOrder && !isAccountManager) {
                        Toast
                            .makeText(context, "请选择下单流程", Toast.LENGTH_SHORT)
                            .show()
                        return@Button
                    }

                    if (multiThreadEnabled) {
                        onConfirm(
                            autoOrderPlaceData,
                            isAccountManager,
                            isAddAddress,
                            isAddCart,
                            isPlaceOrder,
                            multiThreadRangeList
                        )
                        setMultiThreadEnabled(false)
                        FailedTokenIndexRecordUtils.clearFailedTokenIndexes(FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION)
                        onDismiss()
                    } else {
                        Toast
                            .makeText(context, "请配置多线程", Toast.LENGTH_SHORT)
                            .show()
                        return@Button
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 地址配置卡片组件
 *
 * @param isSelected 是否被选中
 * @param autoOrderPlaceData 自动下单数据
 * @param onCardClick 点击卡片的回调，参数表示配置是否有效
 */
@Composable
private fun AddressConfigCard(
    isSelected: Boolean,
    autoOrderPlaceData: AutoOrderPlaceData,
    onCardClick: (Boolean) -> Unit
) {
    val isValid =
        autoOrderPlaceData.addAddressStr.isNotEmpty() && autoOrderPlaceData.addAddressLocationStr.isNotEmpty()

    ConfigCard(
        title = "地址配置",
        isSelected = isSelected,
        onClick = { onCardClick(isValid) }
    ) {
        if (autoOrderPlaceData.addAddressStr.isNotEmpty()) {
            val addressJs = JSONObject(autoOrderPlaceData.addAddressStr)
            val displayStr =
                addressJs.getString("city") + " " +
                        addressJs.getString("district") + " " +
                        addressJs.getString("area") + " " +
                        autoOrderPlaceData.randomDetailsRegex + " " +
                        autoOrderPlaceData.addAddressShopItem?.shopname

            Text(
                text = displayStr,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState()),
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
        }
    }
}

/**
 * 账号配置卡片组件
 *
 * @param isSelected 是否被选中
 * @param autoOrderPlaceData 自动下单数据
 * @param onCardClick 点击卡片的回调，参数表示配置是否有效
 */
@Composable
private fun AccountConfigCard(
    isSelected: Boolean,
    autoOrderPlaceData: AutoOrderPlaceData,
    onCardClick: (Boolean) -> Unit
) {
    val isValid = autoOrderPlaceData.couponPromotionCodeStr.isNotEmpty() ||
            autoOrderPlaceData.newPersonCouponShopParams.isNotEmpty()

    ConfigCard(
        title = "账号配置",
        isSelected = isSelected,
        onClick = { onCardClick(isValid) }
    ) {
        val showStr = buildString {
            if (autoOrderPlaceData.couponPromotionCodeStr.isNotEmpty())
                append("领取 ${autoOrderPlaceData.couponPromotionCodeStr} ")
            if (autoOrderPlaceData.newPersonCouponShopParams.isNotEmpty())
                append("新人券 ${autoOrderPlaceData.newPersonCouponShopParams}")
        }

        if (showStr.isNotEmpty()) {
            Text(
                text = showStr,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState()),
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
        }
    }
}

/**
 * 商品配置卡片组件
 *
 * @param isSelected 是否被选中
 * @param autoOrderPlaceData 自动下单数据
 * @param onCardClick 点击卡片的回调，参数表示配置是否有效
 */
@Composable
private fun GoodsConfigCard(
    isSelected: Boolean,
    autoOrderPlaceData: AutoOrderPlaceData,
    onCardClick: (Boolean) -> Unit
) {
    val isValid = autoOrderPlaceData.productConfigStr.isNotEmpty()

    ConfigCard(
        title = "商品配置",
        isSelected = isSelected,
        onClick = { onCardClick(isValid) }
    ) {
        if (autoOrderPlaceData.productConfigStr.isNotEmpty()) {
            val displayStr =
                autoOrderPlaceData.productConfigStr + if (isSelected) " 勾选加购并匹配" else " 不勾选仅匹配"
            Text(
                text = displayStr,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState()),
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
        }
    }
}

/**
 * 订单配置卡片组件
 *
 * @param isSelected 是否被选中
 * @param autoOrderPlaceData 自动下单数据
 * @param onCardClick 点击卡片的回调，参数表示配置是否有效
 */
@Composable
private fun OrderConfigCard(
    isSelected: Boolean,
    autoOrderPlaceData: AutoOrderPlaceData,
    onCardClick: (Boolean) -> Unit
) {
    val isValid = autoOrderPlaceData.expectTime.isNotEmpty() &&
            autoOrderPlaceData.totalPaymentNew.isNotEmpty()

    ConfigCard(
        title = "订单配置",
        isSelected = isSelected,
        onClick = { onCardClick(isValid) }
    ) {
        if (autoOrderPlaceData.totalPaymentNew.isNotEmpty()) {
            val deliveryTime = extractDeliveryTime(autoOrderPlaceData.expectTime)
            val pickSelfStr = if (autoOrderPlaceData.isPickSelf == 1) "自提" else "配送"

            val displayStr = buildString {
                append("商品金额${autoOrderPlaceData.productsTotalAmount} ")
                append("实付${autoOrderPlaceData.totalPaymentNew} ")
                append("$pickSelfStr ")
                append("$deliveryTime ")

                if (autoOrderPlaceData.orderComment.isNotEmpty()) {
                    append("${autoOrderPlaceData.orderComment} ")
                }

                if (autoOrderPlaceData.selectedOrderPlaceCoupon != null) {
                    append(autoOrderPlaceData.selectedOrderPlaceCoupon.showDescription + " ")
                }

                if (autoOrderPlaceData.selectedOrderPlaceRedPacket != null) {
                    append(autoOrderPlaceData.selectedOrderPlaceRedPacket.showDescription)
                }
            }

            Text(
                text = displayStr,
                modifier = Modifier
                    .horizontalScroll(rememberScrollState()),
                fontSize = 12.sp
            )
        }
    }
}

/**
 * 通用配置卡片基础组件
 *
 * @param title 卡片标题
 * @param isSelected 是否选中状态
 * @param onClick 点击回调
 * @param content 卡片内容
 */
@Composable
private fun ConfigCard(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xCBF7DEF4) else CardContainerColor,
        ),
        modifier = Modifier
            .padding(vertical = 2.dp)
            .height(62.dp)
            .fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxSize()
                .clickable(onClick = onClick)
        ) {
            Column(
                modifier = Modifier
                    .padding(start = 10.dp, end = 8.dp)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    lineHeight = 16.sp
                )
                content()
            }
        }
    }
}

/**
 * 从期望送达时间字符串中提取可读的送达时间
 *
 * @param tExpectTimeJson 期望送达时间的JSON字符串
 * @return 格式化后的送达时间
 */
private fun extractDeliveryTime(tExpectTimeJson: String): String {
    if (tExpectTimeJson.isEmpty()) return ""

    return if (tExpectTimeJson.contains("尽快送达")) {
        "尽快送达"
    } else try {
        val jsonObject = JSONObject(tExpectTimeJson)
        val timeslots = jsonObject.getJSONArray("timeslots")
        val date = jsonObject.getLong("date")

        val dateFormat = SimpleDateFormat("MM.dd", Locale.getDefault())
        val dateStr = dateFormat.format(date)

        "$dateStr,${timeslots.getJSONObject(0).getString("from")}-${
            timeslots.getJSONObject(0).getString("to")
        }"
    } catch (e: Exception) {
        ""
    }
}

/**
 * 自动下单数据类
 */
data class AutoOrderPlaceData(
    // 地址相关
    val addAddressStr: String = "",
    val addAddressLocationStr: String = "",
    val randomDetailsRegex: String = "",
    // 支付选项
    val balancePayOption: Int = 0,
    val pointPayOption: Int = 0,
    // 优惠券
    val couponPromotionCodeStr: String = "",
    // 新人优惠券
    val newPersonCouponShopParams: String = "",
    // 购物车
    val productConfigStr: String = "",
    // 送达时间
    val expectTime: String = "",
    // 配送/自提
    val isPickSelf: Int = 0,
    // 备注
    val orderComment: String = "",
    // 红包、优惠券
    val selectedOrderPlaceCoupon: OrderPlaceCoupon? = null,
    val selectedOrderPlaceRedPacket: OrderPlaceRedPacket? = null,
    // 校验
    val productsTotalAmount: String = "",
    val totalPaymentNew: String = "",
    val addAddressShopItem: ShopInfo? = null
)

/**
 * 多线程配置对话框
 *
 * @param onDismiss 对话框关闭回调
 * @param onConfirm 确认按钮回调，参数为配置的线程范围列表
 * @param multiThreadRangeListStr 当前配置的线程范围列表
 * @param maxThread 最大允许的线程数
 */
@Composable
fun MultiThreadDialog(
    onDismiss: () -> Unit,
    onConfirm: (List<Int>, String) -> Unit,
    multiThreadRangeListStr: String,
    maxThread: Int
) {
    val context = LocalContext.current

    // 高级模式的状态变量
    var tmpMultiThreadRangeListStr by remember { mutableStateOf(multiThreadRangeListStr) }

    // 输入验证状态
    var isInputValid by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var threadCount by remember { mutableStateOf(0) }

    // 验证输入函数
    fun validateInput(input: String): Triple<Boolean, String, List<Int>> {
        if (input.isBlank()) {
            return Triple(false, "", emptyList())
        }

        try {
            val multiThreadRangeArray = mutableListOf<Int>()

            // 解析输入的线程范围
            val segments = if (input.contains(",")) input.split(",") else listOf(input)

            for (segment in segments) {
                val trimmedSegment = segment.trim()
                if (trimmedSegment.isEmpty()) continue

                if (trimmedSegment.contains("-")) {
                    val rangeArray = trimmedSegment.split("-")
                    if (rangeArray.size != 2) {
                        return Triple(false, "范围格式错误", emptyList())
                    }

                    val start = rangeArray[0].trim().toIntOrNull() ?: return Triple(
                        false,
                        "数字格式错误",
                        emptyList()
                    )
                    val end = rangeArray[1].trim().toIntOrNull() ?: return Triple(
                        false,
                        "数字格式错误",
                        emptyList()
                    )

                    if (start > end) {
                        return Triple(false, "范围需从小到大", emptyList())
                    }

                    for (i in start..end) {
                        multiThreadRangeArray.add(i)
                    }
                } else {
                    val num = trimmedSegment.toIntOrNull() ?: return Triple(
                        false,
                        "数字格式错误",
                        emptyList()
                    )
                    multiThreadRangeArray.add(num)
                }
            }

            // 验证结果
            val distinctThreads = multiThreadRangeArray.distinct()

            if (distinctThreads.any { it <= 0 }) {
                return Triple(false, "线程数必须大于0", emptyList())
            }

            val max = distinctThreads.maxOrNull()
            if (max != null && max > maxThread) {
                return Triple(false, "线程范围不能超过最大允许值: $maxThread", emptyList())
            }

            return Triple(true, "", distinctThreads)
        } catch (e: Exception) {
            return Triple(false, "格式错误", emptyList())
        }
    }

    // 当输入变化时验证
    fun updateValidation(input: String) {
        val (valid, message, threads) = validateInput(input)
        isInputValid = valid
        errorMessage = message
        threadCount = threads.size
    }

    // 初始验证
    updateValidation(tmpMultiThreadRangeListStr)

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = {
            Text("多线程配置")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            ) {
                // 高级模式 - 支持复杂范围表达式
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "多线程范围 (1-$maxThread)",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )

                    OutlinedTextField(
                        value = tmpMultiThreadRangeListStr,
                        onValueChange = {
                            tmpMultiThreadRangeListStr = it
                            updateValidation(it)
                        },
                        label = { Text("线程范围 (格式: X-XX,XX,XX)") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        isError = tmpMultiThreadRangeListStr.isNotEmpty() && !isInputValid
                    )

                    // 错误信息 / 线程数量
                    if (tmpMultiThreadRangeListStr.isNotEmpty()) {
                        if (!isInputValid) {
                            Text(
                                text = errorMessage,
                                color = Color.Red,
                                fontSize = 12.sp,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        } else {
                            Text(
                                text = "线程数量: $threadCount",
                                fontSize = 12.sp,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }

                    // 说明文本
                    Text(
                        text = "说明: 支持以下格式:\n" +
                                "- 单个数字: 5\n" +
                                "- 范围: 1-5\n" +
                                "- 组合: 1-3,5,7-10",
                        fontSize = 12.sp,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    try {
                        val (_, _, threads) = validateInput(tmpMultiThreadRangeListStr)
                        onConfirm(threads.sorted(), tmpMultiThreadRangeListStr)
                    } catch (e: Exception) {
                        Toast.makeText(context, "处理参数时出错", Toast.LENGTH_SHORT).show()
                    }
                },
                enabled = isInputValid
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}